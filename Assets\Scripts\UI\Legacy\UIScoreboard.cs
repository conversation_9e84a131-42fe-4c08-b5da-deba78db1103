using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Fusion;

namespace SimpleFPS {
    public class UIScoreboard : MonoBehaviour {
        public TextMeshProUGUI TotalPlayersText;
        public UIScoreboardRow Row;
        public float DisconnectedPlayerAlpha = 0.4f;

        List<UIScoreboardRow> _rows = new(32);
        List<PlayerData> _players = new(32);

        private UIGame _gameUI;

        public static UIScoreboard Instance { get; private set; }

        private void Awake() {
            Instance = this;
            Row.gameObject.SetActive(false);
            _rows.Add(Row);

            _gameUI = GetComponentInParent<UIGame>();
        }

        private void OnEnable() {
            InvokeRepeating(nameof(UpdateScoreboard), 0f, 0.5f);

            // Apply all mute states when scoreboard is enabled (scene loaded)
            if (MuteManager.Instance != null) {
                MuteManager.Instance.ApplyAllMuteStates();
            }
        }

        private void OnDisable() {
            CancelInvoke();
        }

        private void UpdateScoreboard() {
            if (_gameUI.Runner == null)
                return;

            _players.Clear();

            foreach (var record in _gameUI.GameManager.PlayerData) {
                _players.Add(record.Value);
            }

            _players.Sort((a, b) => a.StatisticPosition.CompareTo(b.StatisticPosition));

            TotalPlayersText.text = $"PLAYERS ({_players.Count})";
            PrepareRows(_players.Count);
            UpdateRows();
        }

        private void PrepareRows(int playerCount) {
            // Add missing rows
            for (int i = _rows.Count; i < playerCount; i++) {
                var row = Instantiate(Row, Row.transform.parent);
                row.gameObject.SetActive(true);

                _rows.Add(row);
            }

            // Activate correct count of rows
            for (int i = 0; i < _rows.Count; i++) {
                _rows[i].gameObject.SetActive(i < playerCount);
            }
        }

        private void UpdateRows() {
            for (int i = 0; i < _players.Count; i++) {
                var row = _rows[i];
                var data = _players[i];

                row.Name.text = data.Nickname;
                row.Kills.text = data.Kills.ToString();
                row.Deaths.text = data.Deaths.ToString();
                row.Position.text = data.StatisticPosition < int.MaxValue ? $"#{data.StatisticPosition}" : "-";
                row.PlayerRef = data.PlayerRef;

                row.DeadGroup.SetActive(data.IsAlive == false || data.IsConnected == false);
                row.LocalPlayerGroup.SetActive(_gameUI.Runner.LocalPlayer == data.PlayerRef);

                // Show mute button for all players including local player
                if (row.MuteButton != null) {
                    row.MuteButton.gameObject.SetActive(true);

                    bool isMuted;
                    if (_gameUI.Runner.LocalPlayer == data.PlayerRef) {
                        // For local player, check self mute state
                        isMuted = MuteManager.Instance.IsSelfMuted();
                    }
                    else {
                        // For other players, check if they are muted by local player
                        isMuted = MuteManager.Instance.IsPlayerMuted(data.PlayerRef);
                    }
                    row.UpdateMuteButtonState(isMuted);
                }

                row.CanvasGroup.alpha = data.IsConnected ? 1f : DisconnectedPlayerAlpha;
            }
        }

        public void TogglePlayerMute(PlayerRef playerRef) {
            if (playerRef == _gameUI.Runner.LocalPlayer) {
                // Self mute for local player
                MuteManager.Instance.ToggleSelfMute();
            }
            else {
                // Mute other player
                MuteManager.Instance.TogglePlayerMute(playerRef);
            }
        }

    }
}
