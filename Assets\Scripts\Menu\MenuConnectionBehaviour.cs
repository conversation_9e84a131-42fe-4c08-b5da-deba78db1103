using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.SceneManagement;
using Fusion;
using Fusion.Menu;
using Fusion.Photon.Realtime;
using Fusion.Sockets;
using System.Linq;

#pragma warning disable 1998
#pragma warning disable 4014

namespace SimpleFPS {
    public class MenuConnectionBehaviour : FusionMenuConnectionBehaviour {
        public NetworkRunner RunnerPrefab;
        public MenuUIController UIController;
        public MenuConnectionPlugin ConnectionPlugin;

        public override IFusionMenuConnection Create() {
            if (ConnectionPlugin != null)
                return ConnectionPlugin.Create(this);

            return new MenuConnection(this);
        }
    }

    public class MenuConnection : IFusionMenuConnection {
        public bool IsSessionOwner => _runner != null && _runner.IsRunning == true ? _runner.IsSceneAuthority : default;
        public string SessionName => _runner != null && _runner.IsRunning == true ? _runner.SessionInfo.Name : default;
        public int MaxPlayerCount => _runner != null && _runner.IsRunning == true ? _runner.SessionInfo.MaxPlayers : default;
        public string Region => _runner != null && _runner.IsRunning == true ? _runner.SessionInfo.Region : default;
        public string AppVersion => PhotonAppSettings.Global.AppSettings.AppVersion;
        public List<string> Usernames => default;
        public bool IsConnected => _runner != null ? _runner.IsConnectedToServer : default;

        // Room listing functionality
        public List<SimpleFPS.RoomInfo> AvailableRooms { get; private set; } = new List<SimpleFPS.RoomInfo>();
        public event System.Action<List<SimpleFPS.RoomInfo>> OnRoomListUpdated;
        public int Ping => _runner != null && _runner.IsRunning == true ? Mathf.RoundToInt((float)(_runner.GetPlayerRtt(PlayerRef.None) * 1000.0)) : default;

        protected FusionMenuConnectionBehaviour ConnectionBehaviour => _connectionBehaviour;

        private MenuConnectionBehaviour _connectionBehaviour;
        private NetworkRunner _runner;

        public MenuConnection(MenuConnectionBehaviour connectionBehaviour) {
            _connectionBehaviour = connectionBehaviour;
        }

        public virtual async Task<List<FusionMenuOnlineRegion>> RequestAvailableOnlineRegionsAsync(IFusionMenuConnectArgs connectArgs) {
            List<FusionMenuOnlineRegion> regions = new List<FusionMenuOnlineRegion>();
            foreach (var region in _connectionBehaviour.UIController.Config.AvailableRegions) {
                regions.Add(new FusionMenuOnlineRegion { Code = region, Ping = 0 });
            }

            return regions;
        }

        public virtual async Task<ConnectResult> ConnectAsync(IFusionMenuConnectArgs connectionArgs) {
            Debug.Log("[MenuConnection] ConnectAsync: Starting connection process");

            // CRITICAL FIX: Validate connection args
            if (connectionArgs == null) {
                Debug.LogError("[MenuConnection] ConnectAsync: Connection args is null");
                return ConnectionFail(ConnectFailReason.UserRequest);
            }

            if (string.IsNullOrEmpty(PhotonAppSettings.Global.AppSettings.AppIdFusion) == true) {
                Debug.LogError("[MenuConnection] ConnectAsync: Fusion AppId is missing");
                await _connectionBehaviour.UIController.PopupAsync("The Fusion AppId is missing in PhotonAppSettings. Please follow setup instructions before running the game.", "Game not configured");
                _connectionBehaviour.UIController.Show<FusionMenuUIMain>();
                return ConnectionFail(ConnectFailReason.UserRequest);
            }

            // CRITICAL FIX: Ensure clean state before connecting
            if (_runner != null && _runner.IsRunning) {
                Debug.LogWarning("[MenuConnection] ConnectAsync: Runner is already running, disconnecting first");
                await DisconnectAsync(ConnectFailReason.UserRequest);
                await Task.Delay(500); // Wait for clean shutdown
            }

            _runner = CreateRunner();

            if (_runner == null) {
                Debug.LogError("[MenuConnection] ConnectAsync: Failed to create runner");
                return ConnectionFail(ConnectFailReason.Disconnect);
            }

            var appSettings = PhotonAppSettings.Global.AppSettings.GetCopy();

            // CRITICAL FIX: Ensure region consistency
            if (!string.IsNullOrEmpty(connectionArgs.Region)) {
                appSettings.FixedRegion = connectionArgs.Region;
                Debug.Log($"[MenuConnection] ConnectAsync: Using connectionArgs region {connectionArgs.Region}");
            }
            else {
                var preferredRegion = PlayerPrefs.GetString("Photon.Menu.Region");
                if (!string.IsNullOrEmpty(preferredRegion)) {
                    appSettings.FixedRegion = preferredRegion;
                    connectionArgs.Region = preferredRegion; // Update connectionArgs for consistency
                    Debug.Log($"[MenuConnection] ConnectAsync: Using preferred region {preferredRegion}");
                }
                else {
                    Debug.LogWarning("[MenuConnection] ConnectAsync: No region specified, using default");
                }
            }

            var startGameArgs = new StartGameArgs() {
                SessionName = connectionArgs.Session,
                PlayerCount = connectionArgs.MaxPlayerCount,
                GameMode = GetGameMode(connectionArgs),
                CustomPhotonAppSettings = appSettings,
                EnableClientSessionCreation = true,  // This is required for room creation!
                IsVisible = true,  // Make room visible in lobby
                IsOpen = true      // Make sure room is open for joining
            };

            if (connectionArgs.Creating == false && string.IsNullOrEmpty(connectionArgs.Session) == true) {
                startGameArgs.EnableClientSessionCreation = false;

                var randomJoinResult = await StartRunner(startGameArgs);
                if (randomJoinResult.Success) {
                    return await StartGame(connectionArgs.Scene.SceneName);
                }

                if (randomJoinResult.FailReason == ConnectFailReason.UserRequest)
                    return ConnectionFail(randomJoinResult.FailReason);

                connectionArgs.Creating = true;
                _runner = CreateRunner();

                startGameArgs.EnableClientSessionCreation = true;
                startGameArgs.SessionName = _connectionBehaviour.UIController.Config.CodeGenerator.Create();
                startGameArgs.GameMode = GetGameMode(connectionArgs);
            }
            else {
                startGameArgs.EnableClientSessionCreation = true;
            }

            var result = await StartRunner(startGameArgs);

            if (result.Success) {
                return await StartGame(connectionArgs.Scene.SceneName);
            }

            await DisconnectAsync(result.FailReason);
            return ConnectionFail(result.FailReason);
        }

        public virtual async Task DisconnectAsync(int reason) {
            var runner = _runner;
            _runner = null;

            if (runner != null) {
                Scene sceneToUnload = default;

                if (runner.IsSceneAuthority == true && runner.TryGetSceneInfo(out NetworkSceneInfo sceneInfo) == true) {
                    foreach (var sceneRef in sceneInfo.Scenes) {
                        await runner.UnloadScene(sceneRef);
                    }
                }
                else {
                    sceneToUnload = runner.SceneManager.MainRunnerScene;
                }

                await runner.Shutdown();

                // Safe scene unloading with null checks
                if (sceneToUnload.IsValid() == true && sceneToUnload.isLoaded == true) {
                    if (_connectionBehaviour != null && _connectionBehaviour.gameObject != null) {
                        var connectionScene = _connectionBehaviour.gameObject.scene;
                        if (sceneToUnload != connectionScene) {
                            SceneManager.SetActiveScene(connectionScene);
                            SceneManager.UnloadSceneAsync(sceneToUnload);
                        }
                    }
                }
            }

            // Safe UI handling with null checks
            if (_connectionBehaviour != null && _connectionBehaviour.UIController != null) {
                if (reason != ConnectFailReason.UserRequest) {
                    try {
                        await _connectionBehaviour.UIController.PopupAsync(reason.ToString(), "Disconnected");
                    }
                    catch (System.Exception e) {
                        // UI may be destroyed
                    }
                }

                try {
                    _connectionBehaviour.UIController.OnGameStopped();
                }
                catch (System.Exception e) {
                    // UI may be destroyed
                }
            }
        }

        // Real room listing using Photon Fusion lobby system
        public virtual async Task<List<SimpleFPS.RoomInfo>> RequestRoomListAsync() {
            return await RequestRoomListAsync(null);
        }

        public virtual async Task<List<SimpleFPS.RoomInfo>> RequestRoomListAsync(string region) {
            var roomList = new List<SimpleFPS.RoomInfo>();

            // CRITICAL FIX: Check if we already have an active runner that might conflict
            if (_runner != null && _runner.IsRunning) {
                Debug.LogWarning("[MenuConnection] RequestRoomListAsync: Main runner is active, this might cause conflicts");
            }

            // Retry logic for slow connections
            var maxRetries = 3;
            var retryDelay = 2000; // 2 seconds between retries

            for (int attempt = 1; attempt <= maxRetries; attempt++) {
                Debug.Log($"[MenuConnection] RequestRoomListAsync attempt {attempt}/{maxRetries}");

                try {
                    var lobbyRunner = CreateRunner();
                    if (lobbyRunner == null) {
                        Debug.LogError("[MenuConnection] RequestRoomListAsync: Failed to create lobby runner");
                        return roomList;
                    }

                    var appSettings = PhotonAppSettings.Global.AppSettings.GetCopy();

                    // CRITICAL FIX: Use the same region as the main connection
                    if (!string.IsNullOrEmpty(region)) {
                        appSettings.FixedRegion = region;
                        Debug.Log($"[MenuConnection] RequestRoomListAsync: Using region {region}");
                    }
                    else if (_runner != null && _runner.IsRunning && !string.IsNullOrEmpty(_runner.SessionInfo.Region)) {
                        appSettings.FixedRegion = _runner.SessionInfo.Region;
                        Debug.Log($"[MenuConnection] RequestRoomListAsync: Using current session region {_runner.SessionInfo.Region}");
                    }
                    else {
                        // Use the region from PlayerPrefs (same as FusionMenuConnectArgs.PreferredRegion)
                        var preferredRegion = PlayerPrefs.GetString("Photon.Menu.Region");
                        if (!string.IsNullOrEmpty(preferredRegion)) {
                            appSettings.FixedRegion = preferredRegion;
                            Debug.Log($"[MenuConnection] RequestRoomListAsync: Using preferred region {preferredRegion}");
                        }
                        else {
                            Debug.LogWarning("[MenuConnection] RequestRoomListAsync: No region specified, using default");
                        }
                    }

                    var sessionListHandler = new LobbySessionListHandler();
                    lobbyRunner.AddCallbacks(sessionListHandler);

                    // CRITICAL FIX: Use consistent lobby type - always use ClientServer
                    var lobbyResult = await lobbyRunner.JoinSessionLobby(SessionLobby.ClientServer);

                    if (!lobbyResult.Ok) {
                        Debug.LogError($"[MenuConnection] Failed to join lobby: {lobbyResult.ErrorMessage}");
                        await lobbyRunner.Shutdown();
                        return roomList;
                    }

                    Debug.Log("[MenuConnection] Successfully joined lobby, waiting for session list...");

                    // IMPROVED: Longer timeout and adaptive delay for slow connections
                    var timeout = 60.0f; // Increased from 30 to 60 seconds
                    var startTime = Time.time;
                    var checkInterval = 1000; // Start with 1 second intervals

                    while (Time.time - startTime < timeout) {
                        await Task.Delay(checkInterval);

                        if (sessionListHandler.SessionList != null) {
                            Debug.Log($"[MenuConnection] Received session list with {sessionListHandler.SessionList.Count} sessions after {Time.time - startTime:F1} seconds");
                            break;
                        }

                        // Adaptive interval: increase delay for slow connections
                        var elapsed = Time.time - startTime;
                        if (elapsed > 10.0f) {
                            checkInterval = 2000; // 2 seconds after 10s
                        }

                        Debug.Log($"[MenuConnection] Still waiting for session list... ({elapsed:F1}s elapsed)");
                    }

                    if (sessionListHandler.SessionList != null) {
                        foreach (var sessionInfo in sessionListHandler.SessionList) {
                            // Check if this is a generated session code (private room) or custom name (public room)
                            bool isPrivateRoom = _connectionBehaviour.UIController.Config.CodeGenerator != null &&
                                               _connectionBehaviour.UIController.Config.CodeGenerator.IsValid(sessionInfo.Name);

                            var roomInfo = new SimpleFPS.RoomInfo(
                                sessionInfo.Name, // Show the session code/name
                                sessionInfo.PlayerCount,
                                sessionInfo.MaxPlayers,
                                sessionInfo.IsOpen,
                                sessionInfo.IsVisible
                            );

                            // Store session name for joining
                            roomInfo.CustomProperties["SessionName"] = sessionInfo.Name;
                            roomInfo.CustomProperties["IsPrivate"] = isPrivateRoom;
                            roomInfo.CustomProperties["Region"] = sessionInfo.Region;

                            // Copy custom properties
                            if (sessionInfo.Properties != null) {
                                foreach (var prop in sessionInfo.Properties) {
                                    roomInfo.CustomProperties[prop.Key] = prop.Value.PropertyValue;
                                }
                            }

                            roomList.Add(roomInfo);
                            Debug.Log($"[MenuConnection] Found room: {sessionInfo.Name} ({sessionInfo.PlayerCount}/{sessionInfo.MaxPlayers}) in region {sessionInfo.Region}");
                        }
                    }
                    else {
                        Debug.LogWarning("[MenuConnection] Session list is null after timeout");
                    }

                    await lobbyRunner.Shutdown();

                    // If we got rooms, return immediately
                    if (roomList.Count > 0) {
                        Debug.Log($"[MenuConnection] RequestRoomListAsync completed successfully, found {roomList.Count} rooms");
                        return roomList;
                    }
                }
                catch (System.Exception e) {
                    Debug.LogError($"[MenuConnection] RequestRoomListAsync attempt {attempt} failed: {e.Message}");

                    // If this is the last attempt, don't wait
                    if (attempt < maxRetries) {
                        Debug.Log($"[MenuConnection] Retrying in {retryDelay}ms...");
                        await Task.Delay(retryDelay);
                    }
                }
            }

            Debug.Log($"[MenuConnection] RequestRoomListAsync completed after {maxRetries} attempts, found {roomList.Count} rooms");
            return roomList;
        }



        public virtual async Task<ConnectResult> JoinRoomAsync(string roomName, IFusionMenuConnectArgs connectionArgs) {
            Debug.Log($"[MenuConnection] JoinRoomAsync: Attempting to join room '{roomName}'");

            // CRITICAL FIX: Validate input parameters
            if (string.IsNullOrEmpty(roomName)) {
                Debug.LogError("[MenuConnection] JoinRoomAsync: Room name is null or empty");
                return ConnectionFail(ConnectFailReason.UserRequest);
            }

            if (connectionArgs == null) {
                Debug.LogError("[MenuConnection] JoinRoomAsync: Connection args is null");
                return ConnectionFail(ConnectFailReason.UserRequest);
            }

            // CRITICAL FIX: Ensure clean state before joining
            if (_runner != null && _runner.IsRunning) {
                Debug.Log("[MenuConnection] JoinRoomAsync: Disconnecting existing runner");
                await DisconnectAsync(ConnectFailReason.UserRequest);

                // Wait a bit to ensure clean shutdown
                await Task.Delay(500);
            }

            _runner = CreateRunner();
            if (_runner == null) {
                Debug.LogError("[MenuConnection] JoinRoomAsync: Failed to create runner");
                return ConnectionFail(ConnectFailReason.Disconnect);
            }

            // CRITICAL FIX: Use the same region logic as RequestRoomListAsync
            var appSettings = PhotonAppSettings.Global.AppSettings.GetCopy();

            // Priority: connectionArgs.Region > PreferredRegion from PlayerPrefs > current session region
            if (!string.IsNullOrEmpty(connectionArgs.Region)) {
                appSettings.FixedRegion = connectionArgs.Region;
                Debug.Log($"[MenuConnection] JoinRoomAsync: Using connectionArgs region {connectionArgs.Region}");
            }
            else {
                var preferredRegion = PlayerPrefs.GetString("Photon.Menu.Region");
                if (!string.IsNullOrEmpty(preferredRegion)) {
                    appSettings.FixedRegion = preferredRegion;
                    Debug.Log($"[MenuConnection] JoinRoomAsync: Using preferred region {preferredRegion}");
                }
                else {
                    Debug.LogWarning("[MenuConnection] JoinRoomAsync: No region specified, using default");
                }
            }

            var startGameArgs = new StartGameArgs {
                GameMode = GameMode.Client,
                SessionName = roomName,
                CustomPhotonAppSettings = appSettings,
                EnableClientSessionCreation = false,  // CRITICAL: Don't create new session, join existing!
                IsVisible = false,  // Client doesn't need to be visible
                IsOpen = false      // Client doesn't need to be open
            };

            Debug.Log($"[MenuConnection] JoinRoomAsync: Starting runner for room '{roomName}' in region '{appSettings.FixedRegion}'");
            var result = await StartRunner(startGameArgs);
            if (result.Success) {
                Debug.Log($"[MenuConnection] JoinRoomAsync: Successfully joined room, loading scene '{connectionArgs.Scene.SceneName}'");
                return await StartGame(connectionArgs.Scene.SceneName);
            }

            Debug.LogError($"[MenuConnection] JoinRoomAsync: Failed to join room '{roomName}'");
            return result;
        }

        private GameMode GetGameMode(IFusionMenuConnectArgs connectionArgs) {
            if (_connectionBehaviour.UIController.SelectedGameMode == GameMode.AutoHostOrClient)
                return connectionArgs.Creating ? GameMode.Host : GameMode.Client;

            return _connectionBehaviour.UIController.SelectedGameMode;
        }

        private NetworkRunner CreateRunner() {
            var runner = GameObject.Instantiate(_connectionBehaviour.RunnerPrefab);
            runner.ProvideInput = true;
            return runner;
        }

        private async Task<ConnectResult> StartRunner(StartGameArgs args) {
            var result = await _runner.StartGame(args);

            if (result.Ok) {
                return new ConnectResult() { Success = true };
            }
            else {
                return new ConnectResult() { Success = false, FailReason = ConnectFailReason.Disconnect };
            }
        }

        private async Task<ConnectResult> StartGame(string sceneName) {
            try {
                _runner.AddCallbacks(new MenuConnectionCallbacks(_connectionBehaviour.UIController, sceneName));

                if (_runner.IsSceneAuthority) {
                    await _runner.LoadScene(sceneName, LoadSceneMode.Additive, LocalPhysicsMode.None, true);
                }

                _connectionBehaviour.UIController.OnGameStarted();
                return ConnectionSuccess();
            }
            catch (ArgumentException e) {
                Debug.LogError($"[MenuConnection] StartGame ArgumentException: {e.Message}");
                await DisconnectAsync(ConnectFailReason.Disconnect);
                return ConnectionFail(ConnectFailReason.Disconnect);
            }
            catch (System.Exception e) {
                Debug.LogError($"[MenuConnection] StartGame Exception: {e.Message}");
                await DisconnectAsync(ConnectFailReason.Disconnect);
                return ConnectionFail(ConnectFailReason.Disconnect);
            }
        }

        private static ConnectResult ConnectionSuccess() => new ConnectResult() { Success = true };
        private static ConnectResult ConnectionFail(int failReason) => new ConnectResult() { FailReason = failReason };

        private class MenuConnectionCallbacks : INetworkRunnerCallbacks {
            public readonly MenuUIController Controller;
            public readonly string SceneName;

            public MenuConnectionCallbacks(MenuUIController controller, string sceneName) {
                Controller = controller;
                SceneName = sceneName;
            }

            public async void OnShutdown(NetworkRunner runner, ShutdownReason shutdownReason) {
                if (shutdownReason == ShutdownReason.DisconnectedByPluginLogic) {
                    Controller.OnGameStopped();
                    Controller.Show<FusionMenuUIMain>();
                    Controller.PopupAsync("Disconnected from the server.", "Disconnected");

                    if (runner.SceneManager != null) {
                        if (runner.SceneManager.MainRunnerScene.IsValid() == true) {
                            SceneRef sceneRef = runner.SceneManager.GetSceneRef(runner.SceneManager.MainRunnerScene.name);
                            runner.SceneManager.UnloadScene(sceneRef);
                        }
                    }
                }
            }

            public void OnObjectExitAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
            public void OnObjectEnterAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
            public void OnPlayerJoined(NetworkRunner runner, PlayerRef player) { }
            public void OnPlayerLeft(NetworkRunner runner, PlayerRef player) { }
            public void OnInput(NetworkRunner runner, NetworkInput input) { }
            public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input) { }
            public void OnConnectedToServer(NetworkRunner runner) { }
            public void OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason) { }
            public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token) { }
            public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason) { }
            public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message) { }
            public void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList) { }
            public void OnCustomAuthenticationResponse(NetworkRunner runner, Dictionary<string, object> data) { }
            public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken) { }
            public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data) { }
            public void OnReliableDataProgress(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress) { }
            public void OnSceneLoadStart(NetworkRunner runner) { }
            public void OnSceneLoadDone(NetworkRunner runner) { }
        }
    }

    /// <summary>
    /// Helper class to capture session list from lobby
    /// </summary>
    public class LobbySessionListHandler : INetworkRunnerCallbacks {
        public List<SessionInfo> SessionList { get; private set; }

        public void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList) {
            SessionList = sessionList;
        }

        // Required INetworkRunnerCallbacks methods (empty implementations)
        public void OnPlayerJoined(NetworkRunner runner, PlayerRef player) { }
        public void OnPlayerLeft(NetworkRunner runner, PlayerRef player) { }
        public void OnInput(NetworkRunner runner, NetworkInput input) { }
        public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input) { }
        public void OnShutdown(NetworkRunner runner, ShutdownReason shutdownReason) { }
        public void OnConnectedToServer(NetworkRunner runner) { }
        public void OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason) { }
        public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token) { }
        public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason) { }
        public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message) { }
        public void OnCustomAuthenticationResponse(NetworkRunner runner, Dictionary<string, object> data) { }
        public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken) { }
        public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data) { }
        public void OnReliableDataProgress(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress) { }
        public void OnSceneLoadDone(NetworkRunner runner) { }
        public void OnSceneLoadStart(NetworkRunner runner) { }
        public void OnObjectExitAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
        public void OnObjectEnterAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
    }
}

