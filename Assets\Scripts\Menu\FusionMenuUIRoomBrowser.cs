using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Fusion.Menu;

#if FUSION_ENABLE_TEXTMESHPRO
using Text = TMPro.TMP_Text;
using InputField = TMPro.TMP_InputField;
#else
using Text = UnityEngine.UI.Text;
using InputField = UnityEngine.UI.InputField;
#endif

namespace SimpleFPS {
    /// <summary>
    /// Room browser screen that displays available public rooms and allows joining them or entering private room codes.
    /// </summary>
    public partial class FusionMenuUIRoomBrowser : FusionMenuUIScreen {
        [Header("Room List")]
        [SerializeField] protected Transform _roomListParent;
        [SerializeField] protected GameObject _roomListItemPrefab; // For public rooms
        [SerializeField] protected GameObject _privateRoomListItemPrefab; // For private rooms
        [SerializeField] protected Button _refreshButton;
        [SerializeField] protected TextMeshProUGUI _statusText;



        [Header("Navigation")]
        [SerializeField] protected Button _createRoomButton;
        [Serial<PERSON>Field] protected Button _backButton;

        [Header("Settings")]
        [Serialize<PERSON>ield] protected float _refreshInterval = 5f;

        private List<GameObject> _roomListItems = new List<GameObject>();
        private bool _isRefreshing = false;
        private float _lastRefreshTime = 0f;

        partial void AwakeUser();
        partial void InitUser();
        partial void ShowUser();
        partial void HideUser();

        public override void Awake() {
            base.Awake();
            AwakeUser();
        }

        public override void Init() {
            base.Init();
            InitUser();
        }

        public override void Show() {
            base.Show();

            _statusText.text = "Loading rooms...";



            // Clear existing room list
            ClearRoomList();

            // Start refreshing room list
            RefreshRoomList();

            ShowUser();
        }

        public override void Hide() {
            base.Hide();
            HideUser();
        }

        private void Update() {
            // Auto-refresh room list periodically, but only if we're not in a loading state
            if (gameObject.activeInHierarchy &&
                Time.time - _lastRefreshTime > _refreshInterval &&
                !_isRefreshing &&
                _statusText.text != "Loading rooms..." &&
                !_statusText.text.StartsWith("Joining")) {

                Debug.Log("[RoomBrowser] Auto-refreshing room list");
                RefreshRoomList();
            }
        }

        /// <summary>
        /// Refresh the room list from the server
        /// </summary>
        protected virtual async void RefreshRoomList() {
            if (_isRefreshing) return;

            _isRefreshing = true;
            _lastRefreshTime = Time.time;
            _statusText.text = "Refreshing... (this may take up to 60 seconds for slow connections)";

            try {
                // Get the actual MenuConnection object from MenuConnectionBehaviour
                IFusionMenuConnection actualConnection = null;

                if (Connection is SimpleFPS.MenuConnectionBehaviour menuBehaviour) {
                    // Create the connection if it doesn't exist
                    if (menuBehaviour.Connection == null) {
                        menuBehaviour.Connection = menuBehaviour.Create();
                    }

                    actualConnection = menuBehaviour.Connection;
                }
                else {
                    actualConnection = Connection;
                }

                // Check if the actual connection has RequestRoomListAsync method
                if (actualConnection != null) {
                    try {
                        // CRITICAL FIX: Use specific method signature to avoid ambiguous match
                        var method = actualConnection.GetType().GetMethod("RequestRoomListAsync", new Type[0]); // Method with no parameters
                        if (method != null) {
                            var task = (Task<List<SimpleFPS.RoomInfo>>)method.Invoke(actualConnection, null);
                            var rooms = await task;

                            Debug.Log($"[RoomBrowser] Successfully retrieved {rooms?.Count ?? 0} rooms from server");
                            UpdateRoomList(rooms);
                            return;
                        }
                        else {
                            Debug.LogWarning("[RoomBrowser] RequestRoomListAsync method not found");
                        }
                    }
                    catch (System.Exception e) {
                        Debug.LogError($"[RoomBrowser] Failed to get room list: {e.Message}\n{e.StackTrace}");
                        _statusText.text = $"Failed to load rooms: {e.Message}";
                        return; // Don't fall back to test rooms on real error
                    }
                }

                // Fallback: Create some test rooms for testing (only if no real connection available)
                Debug.LogWarning("[RoomBrowser] No RequestRoomListAsync method available, using test rooms");
                var testRooms = new List<SimpleFPS.RoomInfo>();

                // Add a test public room
                var publicRoom = new SimpleFPS.RoomInfo("Test Public Room", 1, 4, true, true);
                publicRoom.CustomProperties["SessionName"] = "Test Public Room";
                publicRoom.CustomProperties["IsPrivate"] = false;
                publicRoom.CustomProperties["scene"] = "Lobby";
                testRooms.Add(publicRoom);

                // Add a test private room
                var privateRoom = new SimpleFPS.RoomInfo("ABC12345", 0, 4, true, true);
                privateRoom.CustomProperties["SessionName"] = "ABC12345";
                privateRoom.CustomProperties["IsPrivate"] = true;
                privateRoom.CustomProperties["scene"] = "Lobby";
                testRooms.Add(privateRoom);

                UpdateRoomList(testRooms);
                return; // ВАЖНО: выходим здесь, чтобы не показывать ошибку
            }
            catch (System.Exception e) {
                Debug.LogError($"[RoomBrowser] Unexpected error in RefreshRoomList: {e.Message}\n{e.StackTrace}");
                _statusText.text = $"Failed to load rooms: {e.Message}";
            }
            finally {
                _isRefreshing = false;
            }
        }

        /// <summary>
        /// Update the UI with the new room list
        /// </summary>
        protected virtual void UpdateRoomList(List<SimpleFPS.RoomInfo> rooms) {
            ClearRoomList();

            if (rooms == null || rooms.Count == 0) {
                _statusText.text = "No public rooms available.";
                return;
            }

            _statusText.text = $"Found {rooms.Count} room(s)";

            foreach (var room in rooms) {
                CreateRoomListItem(room);
            }
        }

        /// <summary>
        /// Create a UI item for a room
        /// </summary>
        protected virtual void CreateRoomListItem(SimpleFPS.RoomInfo room) {
            if (_roomListParent == null) {
                return;
            }

            // Check if this is a private room
            bool isPrivateRoom = room.CustomProperties?.ContainsKey("IsPrivate") == true &&
                                (bool)room.CustomProperties["IsPrivate"];

            // Choose the correct prefab
            GameObject prefabToUse = isPrivateRoom ? _privateRoomListItemPrefab : _roomListItemPrefab;

            if (prefabToUse == null) {
                UnityEngine.Debug.LogError($"Missing prefab for {(isPrivateRoom ? "private" : "public")} room!");
                return;
            }

            // Add VerticalLayoutGroup if missing
            var parentLayoutGroup = _roomListParent.GetComponent<UnityEngine.UI.LayoutGroup>();
            var parentVerticalLayout = _roomListParent.GetComponent<UnityEngine.UI.VerticalLayoutGroup>();
            var parentHorizontalLayout = _roomListParent.GetComponent<UnityEngine.UI.HorizontalLayoutGroup>();

            if (parentLayoutGroup == null && parentVerticalLayout == null && parentHorizontalLayout == null) {
                var verticalLayout = _roomListParent.gameObject.AddComponent<UnityEngine.UI.VerticalLayoutGroup>();
                verticalLayout.spacing = 5f;
                verticalLayout.childControlWidth = true;
                verticalLayout.childControlHeight = false;
                verticalLayout.childForceExpandWidth = true;
                verticalLayout.childForceExpandHeight = false;
            }

            var item = Instantiate(prefabToUse, _roomListParent);
            _roomListItems.Add(item);

            // Ensure proper layout
            var rectTransform = item.GetComponent<RectTransform>();
            if (rectTransform != null && (rectTransform.rect.width <= 0 || rectTransform.rect.height <= 0)) {
                rectTransform.sizeDelta = new Vector2(900, 60);

                var layoutElement = item.GetComponent<UnityEngine.UI.LayoutElement>();
                if (layoutElement == null) {
                    layoutElement = item.AddComponent<UnityEngine.UI.LayoutElement>();
                }

                layoutElement.minWidth = 900;
                layoutElement.minHeight = 60;
                layoutElement.preferredWidth = 900;
                layoutElement.preferredHeight = 60;
            }

            if (isPrivateRoom) {
                ConfigurePrivateRoomItem(item, room);
            }
            else {
                ConfigurePublicRoomItem(item, room);
            }
        }

        /// <summary>
        /// Configure a public room item
        /// </summary>
        protected virtual void ConfigurePublicRoomItem(GameObject item, SimpleFPS.RoomInfo room) {
            var roomNameText = item.transform.Find("RoomName")?.GetComponent<Text>();
            var roomNameTMP = item.transform.Find("RoomName")?.GetComponent<TextMeshProUGUI>();
            var playerCountText = item.transform.Find("PlayerCount")?.GetComponent<Text>();
            var playerCountTMP = item.transform.Find("PlayerCount")?.GetComponent<TextMeshProUGUI>();
            var mapNameText = item.transform.Find("MapName")?.GetComponent<Text>();
            var mapNameTMP = item.transform.Find("MapName")?.GetComponent<TextMeshProUGUI>();
            var joinButton = item.transform.Find("JoinButton")?.GetComponent<Button>();

            // Set room name
            if (roomNameText != null) roomNameText.text = room.Name;
            if (roomNameTMP != null) roomNameTMP.text = room.Name;

            // Set player count
            var playerCountStr = $"{room.PlayerCount}/{room.MaxPlayers}";
            if (playerCountText != null) playerCountText.text = playerCountStr;
            if (playerCountTMP != null) playerCountTMP.text = playerCountStr;

            // Set map name
            var sceneName = room.CustomProperties?.ContainsKey("scene") == true ?
                room.CustomProperties["scene"].ToString() : "Unknown";
            if (mapNameText != null) mapNameText.text = sceneName;
            if (mapNameTMP != null) mapNameTMP.text = sceneName;

            // Configure join button
            if (joinButton != null) {
                var sessionName = room.CustomProperties?.ContainsKey("SessionName") == true ?
                    room.CustomProperties["SessionName"].ToString() : room.Name;

                joinButton.onClick.AddListener(() => JoinRoom(sessionName));
                joinButton.interactable = room.PlayerCount < room.MaxPlayers;
            }
        }

        /// <summary>
        /// Configure a private room item
        /// </summary>
        protected virtual void ConfigurePrivateRoomItem(GameObject item, SimpleFPS.RoomInfo room) {
            // Get the PrivateRoomListItem component
            var privateRoomItem = item.GetComponent<PrivateRoomListItem>();
            if (privateRoomItem != null) {
                // Get the stored room name or use session code
                var storedRoomName = PlayerPrefs.GetString($"RoomName_{room.Name}", "");
                var displayName = !string.IsNullOrEmpty(storedRoomName) ?
                    storedRoomName :
                    $"Room{UnityEngine.Random.Range(1, 1000)}";

                // Initialize the private room item
                privateRoomItem.Initialize(displayName, room.Name, this);
                return;
            }

            // Fallback to old method if PrivateRoomListItem component is not found
            var roomNameText = item.transform.Find("RoomName")?.GetComponent<Text>();
            var roomNameTMP = item.transform.Find("RoomName")?.GetComponent<TextMeshProUGUI>();
            var playerCountText = item.transform.Find("PlayerCount")?.GetComponent<Text>();
            var playerCountTMP = item.transform.Find("PlayerCount")?.GetComponent<TextMeshProUGUI>();
            var mapNameText = item.transform.Find("MapName")?.GetComponent<Text>();
            var mapNameTMP = item.transform.Find("MapName")?.GetComponent<TextMeshProUGUI>();
            var joinButton = item.transform.Find("JoinButton")?.GetComponent<Button>();

            // Get the stored room name or generate a default one
            var storedRoomName2 = PlayerPrefs.GetString($"RoomName_{room.Name}", "");
            var displayName2 = !string.IsNullOrEmpty(storedRoomName2) ?
                $"[PRIVATE] {storedRoomName2}" :
                $"[PRIVATE] Room{UnityEngine.Random.Range(1, 1000)}";

            if (roomNameText != null) roomNameText.text = displayName2;
            if (roomNameTMP != null) roomNameTMP.text = displayName2;

            // Set player count
            var playerCountStr = $"{room.PlayerCount}/{room.MaxPlayers}";
            if (playerCountText != null) playerCountText.text = playerCountStr;
            if (playerCountTMP != null) playerCountTMP.text = playerCountStr;

            // Show "Enter Code" placeholder instead of actual code
            var codeDisplay = "Enter Code Required";
            if (mapNameText != null) mapNameText.text = codeDisplay;
            if (mapNameTMP != null) mapNameTMP.text = codeDisplay;

            // Configure join button for private rooms
            if (joinButton != null) {
                // Change button text to indicate it needs a code
                var joinButtonText = joinButton.GetComponentInChildren<Text>();
                var joinButtonTMP = joinButton.GetComponentInChildren<TextMeshProUGUI>();

                if (joinButtonText != null) joinButtonText.text = "Need Code";
                if (joinButtonTMP != null) joinButtonTMP.text = "Need Code";

                joinButton.onClick.RemoveAllListeners();
                joinButton.onClick.AddListener(() => {
                    // Show message that user needs to use the private room code field
                    Controller.PopupAsync("To join this private room, enter the session code in the input field and click Join.", "Private Room");
                });
                joinButton.interactable = room.PlayerCount < room.MaxPlayers;

                // Change button color for private rooms
                var buttonImage = joinButton.GetComponent<UnityEngine.UI.Image>();
                if (buttonImage != null) {
                    buttonImage.color = new Color(0.6f, 0.3f, 0.8f, 1f); // Purple color
                }
            }
        }

        /// <summary>
        /// Clear all room list items
        /// </summary>
        protected virtual void ClearRoomList() {
            foreach (var item in _roomListItems) {
                if (item != null) {
                    Destroy(item);
                }
            }
            _roomListItems.Clear();
        }

        /// <summary>
        /// Join a specific room by name
        /// </summary>
        protected virtual async void JoinRoom(string roomName) {
            Debug.Log($"[RoomBrowser] Attempting to join room: {roomName}");
            _statusText.text = $"Joining {roomName}...";

            Controller.Show<FusionMenuUILoading>();

            try {
                // Get the actual MenuConnection object for joining
                IFusionMenuConnection actualConnection = null;

                if (Connection is SimpleFPS.MenuConnectionBehaviour menuBehaviour) {
                    if (menuBehaviour.Connection == null) {
                        menuBehaviour.Connection = menuBehaviour.Create();
                    }
                    actualConnection = menuBehaviour.Connection;
                }
                else {
                    actualConnection = Connection;
                }

                // Use reflection to call JoinRoomAsync if available
                var joinMethod = actualConnection?.GetType().GetMethod("JoinRoomAsync", new Type[] { typeof(string), typeof(IFusionMenuConnectArgs) });
                if (joinMethod != null) {
                    try {
                        var task = (Task<ConnectResult>)joinMethod.Invoke(actualConnection, new object[] { roomName, ConnectionArgs });
                        var result = await task;

                        Debug.Log($"[RoomBrowser] Join room result - Success: {result.Success}, FailReason: {result.FailReason}");
                        await FusionMenuUIMain.HandleConnectionResult(result, Controller);
                    }
                    catch (System.Reflection.TargetInvocationException tie) {
                        // Unwrap the inner exception for better error reporting
                        var innerException = tie.InnerException ?? tie;
                        Debug.LogError($"[RoomBrowser] Failed to join room '{roomName}': {innerException.Message}\n{innerException.StackTrace}");
                        await Controller.PopupAsync($"Failed to join room: {innerException.Message}", "Connection Error");
                        Controller.Show<FusionMenuUIRoomBrowser>();
                    }
                    catch (System.Exception e) {
                        Debug.LogError($"[RoomBrowser] Failed to join room '{roomName}': {e.Message}\n{e.StackTrace}");
                        await Controller.PopupAsync($"Failed to join room: {e.Message}", "Connection Error");
                        Controller.Show<FusionMenuUIRoomBrowser>();
                    }
                }
                else {
                    Debug.LogError("[RoomBrowser] Connection does not support room joining");
                    await Controller.PopupAsync("Connection does not support room joining.", "Connection Error");
                    Controller.Show<FusionMenuUIRoomBrowser>();
                }
            }
            catch (System.Exception e) {
                Debug.LogError($"[RoomBrowser] Unexpected error in JoinRoom: {e.Message}\n{e.StackTrace}");
                await Controller.PopupAsync($"Unexpected error: {e.Message}", "Error");
                Controller.Show<FusionMenuUIRoomBrowser>();
            }
        }

        /// <summary>
        /// Called when refresh button is pressed
        /// </summary>
        public virtual void OnRefreshButtonPressed() {
            RefreshRoomList();
        }

        /// <summary>
        /// Called when create room button is pressed
        /// </summary>
        public virtual void OnCreateRoomButtonPressed() {
            Controller.Show<FusionMenuUICreateRoom>();
        }

        /// <summary>
        /// Called when back button is pressed
        /// </summary>
        public virtual void OnBackButtonPressed() {
            Controller.Show<FusionMenuUIMain>();
        }
    }
}
